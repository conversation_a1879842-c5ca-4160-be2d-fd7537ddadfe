import sys
import os
import configparser
import re
import platform
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QMenu, QInputDialog
)
from PySide6.QtCore import QThread, Signal, Qt
import time
import funasr

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("LightTTS语音识别客户端")
        self.setGeometry(100, 100, 900, 600)

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 设置外边距
        self.main_layout.setSpacing(15)  # 左右面板之间的间距

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)  # 减少左右边距
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 40)  # 左侧面板占据40%宽度
        self.main_layout.addWidget(self.right_panel, 60)  # 右侧面板占据60%宽度

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("语音识别 (带时间戳和说话人)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。可选择输出 SRT 格式，并可指定说话人数量。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(15)

        # 基础配置区域（模型类型和设备选择）
        basic_config_layout = QVBoxLayout()
        
        # 第一行：模型类型选择
        model_type_layout = QHBoxLayout()
        self.model_type_label = QLabel("模型类型:")
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItem("Paraformer-zh-spk", "paraformer")
        self.model_type_combo.addItem("Seaco Paraformer", "seaco")
        self.model_type_combo.addItem("SenseVoice", "sensevoice")
        self.model_type_combo.setCurrentIndex(0)  # 默认选择第一个
        self.model_type_combo.currentTextChanged.connect(self.on_model_type_changed)
        
        model_type_layout.addWidget(self.model_type_label)
        model_type_layout.addWidget(self.model_type_combo)
        model_type_layout.addStretch()
        
        # 第二行：设备选择
        device_layout = QHBoxLayout()
        self.device_label = QLabel("运行设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItem("CPU", "cpu")
        self.device_combo.addItem("CUDA", "cuda")
        
        # 根据操作系统设置默认设备
        is_mac = platform.system().lower() == 'darwin'
        default_device_index = 0 if is_mac else 1  # Mac默认CPU(0)，其他系统默认CUDA(1)
        self.device_combo.setCurrentIndex(default_device_index)
        
        device_layout.addWidget(self.device_label)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        
        basic_config_layout.addLayout(model_type_layout)
        basic_config_layout.addLayout(device_layout)
        self.left_layout.addLayout(basic_config_layout)
        self.left_layout.addSpacing(10)

        # 热词输入 (仅 Seaco Paraformer 显示)
        self.hotword_label = QLabel("热词 (空格分隔):")
        self.hotword_edit = QLineEdit()
        self.hotword_edit.setPlaceholderText("请输入热词，使用空格分隔")
        self.left_layout.addWidget(self.hotword_label)
        self.left_layout.addWidget(self.hotword_edit)
        self.left_layout.addSpacing(10)

        # 输出模式和说话人选项
        options_layout = QVBoxLayout()  # 改为垂直布局以容纳更多选项
        
        # 第一行：输出模式
        output_mode_layout = QHBoxLayout()
        self.output_mode_label = QLabel("输出模式:")
        self.output_mode_combo = QComboBox()
        self.output_mode_combo.addItem("时间戳格式", "timestamp")  # 更清晰的标签
        self.output_mode_combo.addItem("普通文本", "normal")
        self.output_mode_combo.setCurrentIndex(0)  # 默认选择timestamp
        
        output_mode_layout.addWidget(self.output_mode_label)
        output_mode_layout.addWidget(self.output_mode_combo)
        output_mode_layout.addStretch()
        
        # 第二行：说话人识别选项
        speaker_layout = QHBoxLayout()
        self.speaker_enable_checkbox = QCheckBox("启用说话人识别")
        self.speaker_enable_checkbox.setChecked(True)  # 默认启用
        self.speaker_enable_checkbox.toggled.connect(self.on_speaker_enable_changed)
        
        self.speaker_count_spinbox = QSpinBox()
        self.speaker_count_spinbox.setMinimum(0)
        self.speaker_count_spinbox.setValue(0)
        self.speaker_count_spinbox.setPrefix("说话人数 (0=自动): ")
        
        speaker_layout.addWidget(self.speaker_enable_checkbox)
        speaker_layout.addSpacing(10)
        speaker_layout.addWidget(self.speaker_count_spinbox)
        speaker_layout.addStretch()
        
        options_layout.addLayout(output_mode_layout)
        options_layout.addLayout(speaker_layout)
        self.left_layout.addLayout(options_layout)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)  # 增加垂直间距
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)  # 允许字段增长
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        self.asr_model_combo.setMinimumContentsLength(20)  # 设置最小显示长度
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        self.punc_model_combo = QComboBox()
        self.punc_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.punc_model_combo.setMinimumContentsLength(20)
        
        self.spk_model_combo = QComboBox()
        self.spk_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.spk_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        model_config_layout.addRow("标点模型:", self.punc_model_combo)
        model_config_layout.addRow("说话人模型:", self.spk_model_combo)
        
        self.model_config_group.setMinimumHeight(200)  # 设置最小高度确保显示所有选项
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addStretch(1)  # 在底部添加可伸展空间

        # --- 右侧显示面板 ---
        # 程序状态区域
        status_header_layout = QHBoxLayout()
        self.status_label = QLabel("程序状态")
        self.status_label.setStyleSheet("font-size: 14pt; font-weight: bold;")

        self.clear_status_button = QPushButton("清空状态")
        self.clear_status_button.setMaximumWidth(80)
        self.clear_status_button.clicked.connect(lambda: self.status_text_edit.clear())

        status_header_layout.addWidget(self.status_label)
        status_header_layout.addStretch()
        status_header_layout.addWidget(self.clear_status_button)

        self.status_text_edit = QTextEdit()
        self.status_text_edit.setReadOnly(True)
        self.status_text_edit.setPlaceholderText("程序状态信息将显示在这里...")
        self.status_text_edit.setMaximumHeight(200)  # 限制状态区域高度

        # 识别结果区域
        result_header_layout = QHBoxLayout()
        self.result_label = QLabel("识别结果表格")
        self.result_label.setStyleSheet("font-size: 14pt; font-weight: bold;")

        # 下载按钮
        self.download_txt_button = QPushButton("下载TXT")
        self.download_txt_button.setMaximumWidth(80)
        self.download_txt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_txt_button.clicked.connect(self.download_txt_result)
        self.download_txt_button.setToolTip("下载为纯文本格式")

        self.download_srt_button = QPushButton("下载SRT")
        self.download_srt_button.setMaximumWidth(80)
        self.download_srt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_srt_button.clicked.connect(self.download_srt_result)
        self.download_srt_button.setToolTip("下载为SRT字幕格式")

        result_header_layout.addWidget(self.result_label)
        result_header_layout.addStretch()
        result_header_layout.addWidget(self.download_txt_button)
        result_header_layout.addWidget(self.download_srt_button)

        # 添加可编辑表格
        self.result_table = QTableWidget()
        self.result_table.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked | QTableWidget.EditTrigger.EditKeyPressed)
        self.result_table.itemChanged.connect(self._on_table_item_changed)
        self.result_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_table.customContextMenuRequested.connect(self._show_table_context_menu)

        # 表格操作按钮
        table_actions_layout = QHBoxLayout()

        # 撤销按钮
        self.undo_button = QPushButton("撤销")
        self.undo_button.setEnabled(False)
        self.undo_button.clicked.connect(self._undo_edit)
        self.undo_button.setToolTip("撤销上一次编辑操作")

        # 重置编辑按钮
        self.reset_edits_button = QPushButton("重置到原始")
        self.reset_edits_button.setEnabled(False)
        self.reset_edits_button.clicked.connect(self._reset_table_edits)
        self.reset_edits_button.setToolTip("清除所有编辑，恢复到原始识别结果")

        table_actions_layout.addStretch()
        table_actions_layout.addWidget(self.undo_button)
        table_actions_layout.addWidget(self.reset_edits_button)

        # 添加到右侧面板
        self.right_layout.addLayout(status_header_layout)
        self.right_layout.addWidget(self.status_text_edit)
        self.right_layout.addSpacing(10)  # 增加间距
        self.right_layout.addLayout(result_header_layout)
        self.right_layout.addWidget(self.result_table)
        self.right_layout.addLayout(table_actions_layout)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(350)  # 增加控制面板最小宽度
        self.left_panel.setMaximumWidth(500)  # 增加控制面板最大宽度
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 存储当前识别结果的信息，用于下载功能
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        self.current_output_mode = "timestamp"

        # 编辑历史管理，用于撤销/恢复功能
        self.edited_cells = {}  # 记录被编辑过的单元格 {(row, col): True}
        self.original_table_data = []  # 保存原始表格数据用于重置
        self.original_combo_values = {}  # 保存原始下拉框值
        self.original_text_values = {}  # 保存原始文本值

        # 用于记录编辑状态和当前值
        self.combo_current_values = {}  # 记录下拉框当前值

        # 初始化界面状态（包含模型配置加载）
        self.on_model_type_changed()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.status_text_edit.append(f"已选择音频文件: {file_path}")

    def get_current_model_type(self):
        """获取当前选择的模型类型"""
        return self.model_type_combo.currentData()

    def on_speaker_enable_changed(self):
        """说话人识别启用状态改变时更新界面"""
        enabled = self.speaker_enable_checkbox.isChecked()
        self.speaker_count_spinbox.setVisible(enabled)
        
        # 根据当前模型类型和说话人启用状态更新说话人模型下拉框的可见性
        model_type = self.get_current_model_type()
        if model_type in ["paraformer", "seaco"]:
            # 只有在启用说话人识别时才显示说话人模型选择
            # 但标点模型不受影响
            pass  # 说话人模型的显示/隐藏在 on_model_type_changed 中处理

    def on_model_type_changed(self):
        """模型类型改变时更新界面"""
        model_type = self.get_current_model_type()
        
        # 根据模型类型设置界面显示
        ui_config = {
            "paraformer": {
                "title": "Paraformer 语音识别 \n(带时间戳和说话人)",
                "description": "上传音频文件，选择运行设备和模型进行识别。可选择输出格式，并可启用说话人识别。",
                "show_hotword": False,
                "show_speaker": True,
                "show_punc_spk": True
            },
            "seaco": {
                "title": "Seaco Paraformer 语音识别 \n(带时间戳、说话人和热词)",
                "description": "上传音频文件，选择运行设备和模型进行识别。支持自定义热词、多种输出格式和说话人识别。",
                "show_hotword": True,
                "show_speaker": True,
                "show_punc_spk": True
            },
            "sensevoice": {
                "title": "SenseVoice 语音识别 \n(带时间戳和情感识别)",
                "description": "上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和多种输出格式。",
                "show_hotword": False,
                "show_speaker": False,
                "show_punc_spk": False
            }
        }
        
        config = ui_config[model_type]
        
        # 更新标题和描述
        self.title_label.setText(config["title"])
        self.description_label.setText(config["description"])
        
        # 显示/隐藏组件
        self.hotword_label.setVisible(config["show_hotword"])
        self.hotword_edit.setVisible(config["show_hotword"])
        
        # 说话人相关组件
        self.speaker_enable_checkbox.setVisible(config["show_speaker"])
        speaker_enabled = self.speaker_enable_checkbox.isChecked() and config["show_speaker"]
        self.speaker_count_spinbox.setVisible(speaker_enabled)
        
        # 模型选择组件
        self.punc_model_combo.setVisible(config["show_punc_spk"])
        self.spk_model_combo.setVisible(config["show_punc_spk"])
        
        # 重新加载模型配置
        self.load_model_config()

    def load_model_config(self):
        # 统一的配置文件路径
        config_paths = [
            # os.path.join(os.getcwd(), "app", "model.conf"),
            os.path.join(os.getcwd(), "app", "model_mac.conf")
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.status_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    return

        if not loaded_path:
            self.status_text_edit.append("错误: 未找到 model.conf 文件。请确保它位于项目根目录或 app 目录下。")
            self.asr_model_combo.addItem("错误: 未找到 model.conf")
            self.vad_model_combo.addItem("错误: 未找到 model.conf")
            self.punc_model_combo.addItem("错误: 未找到 model.conf")
            self.spk_model_combo.addItem("错误: 未找到 model.conf")
            return

        self.status_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.status_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        model_type = self.get_current_model_type()
        
        # 根据模型类型选择对应的ASR模型section
        if model_type == "paraformer":
            populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        elif model_type == "seaco":
            populate_combo(self.asr_model_combo, "asr_seaco_models_dir", self.asr_models_config)
        elif model_type == "sensevoice":
            populate_combo(self.asr_model_combo, "asr_sense_model_dir", self.asr_models_config)
        
        # VAD模型对所有类型都需要
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)
        
        # 根据模型类型决定是否加载其他模型
        if model_type in ["paraformer", "seaco"]:
            populate_combo(self.punc_model_combo, "punc_models_dir", self.punc_models_config)
            populate_combo(self.spk_model_combo, "spk_models_dir", self.spk_models_config)
            
            # 为可选模型添加"不使用"选项，但默认选择第一个实际模型
            for combo, model_cfg in [(self.vad_model_combo, self.vad_models_config), 
                                     (self.punc_model_combo, self.punc_models_config), 
                                     (self.spk_model_combo, self.spk_models_config)]:
                if combo.count() > 0 and "无模型" not in combo.itemText(0) and "错误:" not in combo.itemText(0):
                    combo.insertItem(0, "None (不使用)")
                    model_cfg["None (不使用)"] = None 
                    # 默认选择第1个实际模型，而不是"None (不使用)"
                    if combo.count() > 1:
                        combo.setCurrentIndex(1)
        elif model_type == "sensevoice":
            # SenseVoice 只需要 ASR 和 VAD 模型，清空其他模型配置
            self.punc_models_config.clear()
            self.spk_models_config.clear()
            self.punc_model_combo.clear()
            self.spk_model_combo.clear()
            
            # VAD 模型添加不使用选项
            if self.vad_model_combo.count() > 0 and "无模型" not in self.vad_model_combo.itemText(0) and "错误:" not in self.vad_model_combo.itemText(0):
                self.vad_model_combo.insertItem(0, "None (不使用)")
                self.vad_models_config["None (不使用)"] = None 
                if self.vad_model_combo.count() > 1:
                    self.vad_model_combo.setCurrentIndex(1)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.status_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = self.device_combo.currentData()
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()
        punc_model_name = self.punc_model_combo.currentText()
        spk_model_name = self.spk_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)
        punc_model_path = self.punc_models_config.get(punc_model_name)
        spk_model_path = self.spk_models_config.get(spk_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name :
            self.status_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if "错误:" in vad_model_name or "无模型" in vad_model_name or vad_model_name == "None (不使用)": vad_model_path = None
        if "错误:" in punc_model_name or "无模型" in punc_model_name or punc_model_name == "None (不使用)": punc_model_path = None
        if "错误:" in spk_model_name or "无模型" in spk_model_name or spk_model_name == "None (不使用)": spk_model_path = None

        output_mode = self.output_mode_combo.currentData()
        
        # 说话人识别相关参数
        speaker_enabled = self.speaker_enable_checkbox.isChecked() and self.speaker_enable_checkbox.isVisible()
        num_speakers = self.speaker_count_spinbox.value() if speaker_enabled else 0
        
        # 如果禁用说话人识别，强制设置说话人模型路径为 None
        if not speaker_enabled:
            spk_model_path = None
            
        hotword = self.hotword_edit.text().strip() if self.hotword_edit.isVisible() else ""
        model_type = self.get_current_model_type()

        # 清空结果区域，准备显示新的识别结果
        self.result_text_edit.clear()
        self.result_label.setText("识别结果")
        self.download_result_button.setEnabled(False)  # 禁用下载按钮
        
        # 保存当前识别参数用于下载
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        self.current_output_mode = output_mode
        
        # 状态信息输出到状态区域
        self.status_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.status_text_edit.append(f"  模型类型: {self.model_type_combo.currentText()}")
        self.status_text_edit.append(f"  设备: {self.device_combo.currentText()}")
        self.status_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.status_text_edit.append(f"  VAD 模型: {vad_model_name if vad_model_path else '不使用'}")
        
        if model_type in ["paraformer", "seaco"]:
            self.status_text_edit.append(f"  标点模型: {punc_model_name if punc_model_path else '不使用'}")
            if speaker_enabled:
                self.status_text_edit.append(f"  说话人模型: {spk_model_name if spk_model_path else '不使用'}")
                self.status_text_edit.append(f"  说话人数: {num_speakers if num_speakers > 0 else '自动'}")
            else:
                self.status_text_edit.append(f"  说话人识别: 已禁用")
            
        if model_type == "seaco" and hotword:
            self.status_text_edit.append(f"  热词: {hotword}")
            
        self.status_text_edit.append(f"  输出模式: {'时间戳格式' if output_mode == 'timestamp' else '普通文本'}")
        self.status_text_edit.append("")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path, punc_model_path, spk_model_path,
            output_mode, num_speakers, hotword, model_type, self.model_cache, speaker_enabled
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.status_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time, table_data):
        # 状态信息更新
        self.status_text_edit.append(f"识别完成，耗时: {processing_time:.2f} 秒")

        # 更新标题
        self.result_label.setText(f"识别结果表格 (耗时: {processing_time:.2f} 秒)")

        # 显示表格数据
        if table_data:
            self._populate_results_table(table_data)
            self.reset_edits_button.setEnabled(True)
            # 保存原始数据用于重置
            self.original_table_data = table_data.copy()
            # 初始化编辑历史
            self._init_edit_history()

            # 启用下载按钮
            self.download_txt_button.setEnabled(True)
            self.download_srt_button.setEnabled(True)
        else:
            # 如果没有表格数据，显示空表格
            self.result_table.setRowCount(0)
            self.status_text_edit.append("警告: 没有识别到有效的时间戳数据")
            self.reset_edits_button.setEnabled(False)

            # 禁用下载按钮
            self.download_txt_button.setEnabled(False)
            self.download_srt_button.setEnabled(False)

        # 保存识别结果
        self.current_recognition_result = result_text

        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.status_text_edit.append(f"--- 错误 ---")
        self.status_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")



class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float, list)  # 添加 list 参数用于表格数据
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, punc_model_path, spk_model_path,
                 output_mode, num_speakers, hotword, model_type, model_cache_dict, speaker_enabled):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.punc_model_path = punc_model_path
        self.spk_model_path = spk_model_path
        self.output_mode = output_mode
        self.num_speakers = num_speakers
        self.hotword = hotword
        self.model_type = model_type
        self.model_cache = model_cache_dict
        self.speaker_enabled = speaker_enabled

    def _format_timestamp(self, milliseconds, srt_format=False):
        """Converts milliseconds to HH:MM:SS,ms or HH:MM:SS.ms format."""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            model_key_parts = [
                self.device,
                self.asr_model_path,
                str(self.vad_model_path),
                str(self.punc_model_path),
                str(self.spk_model_path),
                self.model_type
            ]
            model_key = "_".join(filter(None, model_key_parts))

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "model": self.asr_model_path,
                    "device": self.device,
                    "disable_update": True
                }
                
                # 根据模型类型设置不同的参数
                if self.model_type == "paraformer":
                    model_kwargs["model_revision"] = "master"
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        model_kwargs["timestamp"] = True
                        
                elif self.model_type == "seaco":
                    model_kwargs["model_revision"] = "master"
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 60000}
                    if self.punc_model_path:
                        model_kwargs["punc_model"] = self.punc_model_path
                    if self.spk_model_path and self.speaker_enabled:
                        model_kwargs["spk_model"] = self.spk_model_path
                        model_kwargs["timestamp"] = True
                        
                elif self.model_type == "sensevoice":
                    if self.vad_model_path:
                        model_kwargs["vad_model"] = self.vad_model_path
                        model_kwargs["vad_kwargs"] = {"max_single_segment_time": 30000}
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            # 根据模型类型设置不同的识别参数
            generate_kwargs = {"input": self.audio_file}
            
            if self.model_type in ["paraformer", "seaco"]:
                generate_kwargs["cache"] = {}
                generate_kwargs["return_raw_text"] = True
                if self.spk_model_path and self.speaker_enabled and self.num_speakers > 0:
                    generate_kwargs['preset_spk_num'] = self.num_speakers
                if self.model_type == "seaco" and self.hotword:
                    generate_kwargs['hotword'] = self.hotword
                    
            elif self.model_type == "sensevoice":
                generate_kwargs.update({
                    "cache": {},
                    "language": "auto",
                    "batch_size_s": 60,
                    "merge_vad": True,
                    "merge_length_s": 15,
                    "output_timestamp": self.output_mode == "timestamp"
                })

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            # 根据模型类型处理结果
            if self.model_type == "sensevoice":
                output_lines, table_data = self._process_sensevoice_result(rec_result)
            else:
                output_lines, table_data = self._process_paraformer_result(rec_result)

            self.finished.emit("\n".join(output_lines), processing_time, table_data)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

    def _process_paraformer_result(self, rec_result):
        """处理 Paraformer 和 Seaco Paraformer 的识别结果"""

        output_lines = []
        table_data = []  # 用于表格显示的数据

        if rec_result:
            subtitle_index = 1

            for result in rec_result:
                if 'sentence_info' in result:
                    for sentence in result['sentence_info']:
                        spk_value = sentence.get('spk')
                        # 只有在启用说话人识别时才显示说话人标签
                        if self.speaker_enabled and spk_value is not None and isinstance(spk_value, int):
                            speaker = f"spk{spk_value + 1}"
                            speaker_formatted = f"[{speaker}]   "
                            speaker_for_table = speaker
                        else:
                            speaker_formatted = ""
                            speaker_for_table = ""

                        text = sentence.get('text', '').strip().rstrip(',.。，!！?？')
                        start_time = self._format_timestamp(sentence.get('start', 0), srt_format=(self.output_mode == "timestamp"))
                        end_time = self._format_timestamp(sentence.get('end', 0), srt_format=(self.output_mode == "timestamp"))

                        if self.output_mode == "timestamp":
                            output_lines.append(str(subtitle_index))
                            output_lines.append(f"{start_time} --> {end_time}")
                            output_lines.append(f"{speaker_formatted}{text}")
                            output_lines.append("")

                            # 为表格添加数据 - 根据说话人识别启用状态生成不同结构
                            if self.speaker_enabled:
                                # 启用说话人识别：5列数据
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    speaker_for_table,
                                    text
                                ])
                            else:
                                # 不启用说话人识别：4列数据
                                table_data.append([
                                    subtitle_index,
                                    start_time,
                                    end_time,
                                    text
                                ])

                            subtitle_index += 1
                        else:
                            output_lines.append(f"{speaker_formatted}{text}")
                elif 'text' in result:
                    # 如果没有sentence_info但有text，创建一个简单的条目
                    text = result.get('text', '').strip()
                    if text:
                        if self.output_mode == "timestamp":
                            # 没有时间戳信息时，使用默认时间戳
                            start_time = "00:00:00,000"
                            end_time = "00:00:01,000"

                            output_lines.append(str(subtitle_index))
                            output_lines.append(f"{start_time} --> {end_time}")
                            output_lines.append(text)
                            output_lines.append("")

                            # 为表格添加数据
                            if self.speaker_enabled:
                                table_data.append([subtitle_index, start_time, end_time, "", text])
                            else:
                                table_data.append([subtitle_index, start_time, end_time, text])

                            subtitle_index += 1
                        else:
                            output_lines.append(text)
        else:
            output_lines.append("识别结果为空。")

        return output_lines, table_data

    def _process_sensevoice_result(self, rec_result):
        """处理 SenseVoice 的识别结果"""

        output_lines = []
        table_data = []  # 用于表格显示的数据

        # 普通模式
        if self.output_mode == "normal":
            texts = [r.get('text', '').strip() for r in rec_result if r.get('text')]
            return texts, []

        # 时间戳模式，构建 SRT 样式
        if rec_result:
            raw = rec_result[0].get('text', '')
            timestamps = rec_result[0].get('timestamp', [])

            # 情感和事件标签
            emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
            event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}

            parts = raw.split('<|zh|>')
            offset = 0
            segments = []

            for part in parts:
                if not part:
                    continue
                m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                if m:
                    tags = ''.join([t for t in re.findall(r'<\|[^|]+\|>', m.group(1)) if t in emo_tags or t in event_tags])
                    content = part[len(m.group(1)):].strip()
                else:
                    tags = ''
                    content = part.strip()

                length = len(content)
                if length == 0:
                    continue

                st = timestamps[offset][0] if offset < len(timestamps) else 0
                et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                segments.append((tags, content, st, et))
                offset += length

            # 格式化输出和表格数据
            for idx, (tags, content, st, et) in enumerate(segments, 1):
                start_time = self._ms2srt(st)
                end_time = self._ms2srt(et)
                full_text = tags + content

                output_lines.append(str(idx))
                output_lines.append(f"{start_time} --> {end_time}")
                output_lines.append(full_text)
                output_lines.append("")

                # 解析情感和事件标签
                emotion_tag = ""
                event_tag = ""
                for tag in re.findall(r'<\|[^|]+\|>', tags):
                    if tag in emo_tags:
                        emotion_tag = tag
                    elif tag in event_tags:
                        event_tag = tag

                # 为表格添加数据 - SenseVoice 使用5列格式
                table_data.append([
                    idx,
                    start_time,
                    end_time,
                    emotion_tag,
                    event_tag,
                    content
                ])

        return output_lines if output_lines else ["识别结果为空。"], table_data

    def _ms2srt(self, ms):
        """SenseVoice 时间戳格式化函数"""
        h = ms // 3600000
        m = (ms % 3600000) // 60000
        s = (ms % 60000) // 1000
        ms_r = ms % 1000
        return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

    def _get_table_headers(self):
        """根据模型类型获取表格列标题"""
        model_type = self.get_current_model_type()

        if model_type == "sensevoice":
            return ["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"]
        elif model_type in ["paraformer", "seaco"]:
            if self.speaker_enable_checkbox.isChecked():
                return ["编号", "开始时间", "结束时间", "说话人", "识别文本"]
            else:
                return ["编号", "开始时间", "结束时间", "识别文本"]
        else:
            return ["编号", "开始时间", "结束时间", "识别文本"]

    def _get_speaker_column_index(self):
        """获取说话人列索引，如果不启用说话人识别则返回-1"""
        model_type = self.get_current_model_type()
        if model_type in ["paraformer", "seaco"] and self.speaker_enable_checkbox.isChecked():
            return 3
        else:
            return -1

    def _get_text_column_index(self):
        """获取识别文本列索引"""
        model_type = self.get_current_model_type()
        if model_type == "sensevoice":
            return 5
        elif model_type in ["paraformer", "seaco"] and self.speaker_enable_checkbox.isChecked():
            return 4
        else:
            return 3

    def _is_column_editable(self, col_idx):
        """判断某列是否可编辑"""
        model_type = self.get_current_model_type()

        if model_type == "sensevoice":
            # SenseVoice: 情感标签(3)、事件标签(4)、识别文本(5)可编辑
            return col_idx in [3, 4, 5]
        elif model_type in ["paraformer", "seaco"]:
            # Paraformer: 说话人列和识别文本列可编辑
            speaker_col = self._get_speaker_column_index()
            text_col = self._get_text_column_index()
            return col_idx == speaker_col or col_idx == text_col
        else:
            # 默认只有识别文本列可编辑
            text_col = self._get_text_column_index()
            return col_idx == text_col

    def _populate_results_table(self, table_data):
        """填充结果表格"""
        if not table_data:
            self.result_table.setRowCount(0)
            return

        # 设置表格列数和标题
        headers = self._get_table_headers()
        self.result_table.setColumnCount(len(headers))
        self.result_table.setHorizontalHeaderLabels(headers)

        # 设置行数
        self.result_table.setRowCount(len(table_data))

        # 定义情感和事件标签（仅SenseVoice使用）
        emotion_labels = ['(无)', '<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>']
        event_labels = ['(无)', '<|BGM|>', '<|Speech|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>']

        # 填充数据
        for row_idx, row_data in enumerate(table_data):
            for col_idx, cell_data in enumerate(row_data):
                if col_idx >= len(headers):
                    break

                model_type = self.get_current_model_type()

                # SenseVoice的情感和事件标签列使用下拉框
                if model_type == "sensevoice" and col_idx in [3, 4]:
                    combo = QComboBox()
                    if col_idx == 3:  # 情感标签列
                        combo.addItems(emotion_labels)
                        labels = emotion_labels
                    else:  # 事件标签列
                        combo.addItems(event_labels)
                        labels = event_labels

                    # 设置当前值
                    current_value = str(cell_data).strip()
                    if current_value == "" or current_value == "None":
                        display_value = "(无)"
                    else:
                        display_value = current_value

                    if display_value in labels:
                        combo.setCurrentText(display_value)
                    else:
                        combo.setCurrentIndex(0)
                        display_value = "(无)"

                    # 记录当前值
                    combo_key = (row_idx, col_idx)
                    self.combo_current_values[combo_key] = display_value
                    self.original_combo_values[combo_key] = display_value

                    # 连接信号
                    combo.currentTextChanged.connect(self._create_combo_handler(row_idx, col_idx))
                    self.result_table.setCellWidget(row_idx, col_idx, combo)

                else:
                    # 普通文本单元格
                    item = QTableWidgetItem(str(cell_data))
                    if self._is_column_editable(col_idx):
                        item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                        item.setToolTip("双击编辑")
                        # 保存原始文本值
                        if col_idx == self._get_text_column_index() or col_idx == self._get_speaker_column_index():
                            text_key = (row_idx, col_idx)
                            self.original_text_values[text_key] = str(cell_data)
                    else:
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        item.setToolTip("此列不可编辑")
                    self.result_table.setItem(row_idx, col_idx, item)

        # 调整列宽
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

        # 设置最小列宽
        for i in range(len(headers)):
            self.result_table.setColumnWidth(i, 120)

        # 识别文本列设置更宽
        text_col = self._get_text_column_index()
        if text_col >= 0:
            self.result_table.setColumnWidth(text_col, 300)

    def _create_combo_handler(self, row, col):
        """创建下拉框变化处理函数"""
        return lambda text: self._on_combo_changed(row, col, text)

    def _on_combo_changed(self, row, col, new_text):
        """处理下拉框选择改变事件"""
        combo_key = (row, col)
        original_value = self.original_combo_values.get(combo_key, "(无)")

        # 更新当前值记录
        self.combo_current_values[combo_key] = new_text

        # 如果新值与原始值不同，标记为已编辑
        if new_text != original_value:
            self.edited_cells[combo_key] = True
        else:
            # 如果恢复到原始值，移除编辑标记
            if combo_key in self.edited_cells:
                del self.edited_cells[combo_key]

        # 更新按钮状态
        self._update_edit_buttons_state()

    def _on_table_item_changed(self, item):
        """处理表格项编辑事件"""
        if not item:
            return

        row = item.row()
        col = item.column()
        new_text = item.text().strip()

        # 只处理可编辑列
        if self._is_column_editable(col):
            text_key = (row, col)
            original_text = self.original_text_values.get(text_key, "")

            # 如果新值与原始值不同，标记为已编辑
            if new_text != original_text:
                self.edited_cells[text_key] = True
            else:
                # 如果恢复到原始值，移除编辑标记
                if text_key in self.edited_cells:
                    del self.edited_cells[text_key]

            # 更新按钮状态
            self._update_edit_buttons_state()

    def _update_edit_buttons_state(self):
        """更新撤销/重置按钮的状态"""
        has_edits = len(self.edited_cells) > 0
        self.undo_button.setEnabled(has_edits)
        # 重置按钮在有表格数据时就启用
        self.reset_edits_button.setEnabled(self.result_table.rowCount() > 0)

    def _init_edit_history(self):
        """初始化编辑历史"""
        self.edited_cells = {}
        self.combo_current_values = {}
        # 注意：不清空原始值，因为它们需要保持原始数据
        self._update_edit_buttons_state()

    def _undo_edit(self):
        """撤销编辑：将最近编辑的单元格恢复到原始值"""
        if not self.edited_cells:
            return

        # 获取最后一个被编辑的单元格（按编辑顺序）
        last_edited_key = list(self.edited_cells.keys())[-1]
        row, col = last_edited_key

        # 根据列类型恢复原始值
        if col in [3, 4] and self.get_current_model_type() == "sensevoice":  # 下拉框列
            combo = self.result_table.cellWidget(row, col)
            if combo:
                original_value = self.original_combo_values.get(last_edited_key, "(无)")

                # 暂时断开信号
                combo.currentTextChanged.disconnect()
                combo.setCurrentText(original_value)
                # 更新当前值记录
                self.combo_current_values[last_edited_key] = original_value
                # 重新连接信号
                combo.currentTextChanged.connect(self._create_combo_handler(row, col))

                col_name = "情感标签" if col == 3 else "事件标签"
                self.status_text_edit.append(f"已撤销第{row+1}行{col_name}到原始值: '{original_value}'")

        else:  # 文本列
            original_text = self.original_text_values.get(last_edited_key, "")

            # 暂时断开信号
            self.result_table.itemChanged.disconnect(self._on_table_item_changed)
            item = self.result_table.item(row, col)
            if item:
                item.setText(original_text)
            # 重新连接信号
            self.result_table.itemChanged.connect(self._on_table_item_changed)

            col_name = "说话人" if col == self._get_speaker_column_index() else "识别文本"
            self.status_text_edit.append(f"已撤销第{row+1}行{col_name}到原始值: '{original_text}'")

        # 移除编辑标记
        del self.edited_cells[last_edited_key]

        # 更新按钮状态
        self._update_edit_buttons_state()

    def _reset_table_edits(self):
        """重置所有编辑，恢复到原始识别结果"""
        if not self.original_table_data:
            return

        # 重新填充表格
        self._populate_results_table(self.original_table_data)

        # 重新初始化编辑历史
        self._init_edit_history()

        self.status_text_edit.append("已重置所有编辑，恢复到原始识别结果")

    def _show_table_context_menu(self, position):
        """显示表格右键菜单"""
        item = self.result_table.itemAt(position)
        if not item:
            return

        menu = QMenu(self)

        # 撤销操作
        if self.edited_cells:
            undo_action = menu.addAction("撤销上一次编辑")
            undo_action.triggered.connect(self._undo_edit)

        # 重置操作
        if self.original_table_data:
            reset_action = menu.addAction("重置到原始结果")
            reset_action.triggered.connect(self._reset_table_edits)

        # 如果是说话人列，添加批量替换功能
        if item.column() == self._get_speaker_column_index():
            menu.addSeparator()
            batch_replace_action = menu.addAction("批量替换说话人")
            batch_replace_action.triggered.connect(lambda: self._batch_replace_speaker(item.row()))

        if menu.actions():
            menu.exec(self.result_table.mapToGlobal(position))

    def _batch_replace_speaker(self, current_row):
        """批量替换说话人"""
        current_item = self.result_table.item(current_row, self._get_speaker_column_index())
        if not current_item:
            return

        current_speaker = current_item.text().strip()

        # 获取新的说话人名称
        new_speaker, ok = QInputDialog.getText(
            self,
            "批量替换说话人",
            f"将所有 '{current_speaker}' 替换为:",
            text=current_speaker
        )

        if ok and new_speaker.strip():
            new_speaker = new_speaker.strip()
            speaker_col = self._get_speaker_column_index()
            replaced_count = 0

            # 暂时断开信号
            self.result_table.itemChanged.disconnect(self._on_table_item_changed)

            # 遍历所有行进行替换
            for row in range(self.result_table.rowCount()):
                item = self.result_table.item(row, speaker_col)
                if item and item.text().strip() == current_speaker:
                    item.setText(new_speaker)

                    # 标记为已编辑
                    text_key = (row, speaker_col)
                    original_text = self.original_text_values.get(text_key, "")
                    if new_speaker != original_text:
                        self.edited_cells[text_key] = True
                    else:
                        if text_key in self.edited_cells:
                            del self.edited_cells[text_key]

                    replaced_count += 1

            # 重新连接信号
            self.result_table.itemChanged.connect(self._on_table_item_changed)

            # 更新按钮状态
            self._update_edit_buttons_state()

            self.status_text_edit.append(f"已将 {replaced_count} 个 '{current_speaker}' 替换为 '{new_speaker}'")

    def download_txt_result(self):
        """下载TXT格式结果"""
        if self.result_table.rowCount() == 0:
            self.status_text_edit.append("没有可下载的结果")
            return

        # 生成文本内容
        lines = []
        model_type = self.get_current_model_type()

        for row in range(self.result_table.rowCount()):
            if model_type == "sensevoice":
                # SenseVoice格式：处理情感和事件标签
                emotion_combo = self.result_table.cellWidget(row, 3)
                event_combo = self.result_table.cellWidget(row, 4)
                text_item = self.result_table.item(row, 5)

                if text_item:
                    emotion = emotion_combo.currentText().strip() if emotion_combo else ""
                    event = event_combo.currentText().strip() if event_combo else ""
                    text = text_item.text().strip()

                    # 处理"(无)"标签，转换为空字符串
                    if emotion == "(无)":
                        emotion = ""
                    if event == "(无)":
                        event = ""

                    # 生成带标签的文本
                    full_text = ""
                    if emotion:
                        full_text += emotion
                    if event:
                        full_text += event
                    full_text += text

                    lines.append(full_text)

            else:
                # Paraformer格式：处理说话人标签
                speaker_col = self._get_speaker_column_index()
                text_col = self._get_text_column_index()

                text_item = self.result_table.item(row, text_col)
                if text_item:
                    text = text_item.text().strip()

                    # 添加说话人标签（如果启用）
                    if speaker_col >= 0:
                        speaker_item = self.result_table.item(row, speaker_col)
                        speaker = speaker_item.text().strip() if speaker_item else ""
                        if speaker:
                            text = f"[{speaker}]   {text}"

                    lines.append(text)

        # 保存文件
        self._save_text_file("\n".join(lines), "txt")

    def download_srt_result(self):
        """下载SRT格式结果"""
        if self.result_table.rowCount() == 0:
            self.status_text_edit.append("没有可下载的结果")
            return

        # 生成SRT内容
        lines = []
        model_type = self.get_current_model_type()

        for row in range(self.result_table.rowCount()):
            # 获取基本信息
            id_item = self.result_table.item(row, 0)
            start_item = self.result_table.item(row, 1)
            end_item = self.result_table.item(row, 2)

            if not all(item is not None for item in [id_item, start_item, end_item]):
                continue

            subtitle_id = id_item.text()
            start_time = start_item.text()
            end_time = end_item.text()

            if model_type == "sensevoice":
                # SenseVoice格式
                emotion_combo = self.result_table.cellWidget(row, 3)
                event_combo = self.result_table.cellWidget(row, 4)
                text_item = self.result_table.item(row, 5)

                if text_item:
                    emotion = emotion_combo.currentText().strip() if emotion_combo else ""
                    event = event_combo.currentText().strip() if event_combo else ""
                    text = text_item.text().strip()

                    # 处理"(无)"标签，转换为空字符串
                    if emotion == "(无)":
                        emotion = ""
                    if event == "(无)":
                        event = ""

                    # 生成带标签的文本
                    full_text = ""
                    if emotion:
                        full_text += emotion
                    if event:
                        full_text += event
                    full_text += text

                    lines.append(subtitle_id)
                    lines.append(f"{start_time} --> {end_time}")
                    lines.append(full_text)
                    lines.append("")

            else:
                # Paraformer格式
                speaker_col = self._get_speaker_column_index()
                text_col = self._get_text_column_index()

                text_item = self.result_table.item(row, text_col)
                if text_item:
                    text = text_item.text().strip()

                    # 添加说话人标签（如果启用）
                    if speaker_col >= 0:
                        speaker_item = self.result_table.item(row, speaker_col)
                        speaker = speaker_item.text().strip() if speaker_item else ""
                        if speaker:
                            text = f"[{speaker}]   {text}"

                    lines.append(subtitle_id)
                    lines.append(f"{start_time} --> {end_time}")
                    lines.append(text)
                    lines.append("")

        # 保存文件
        self._save_text_file("\n".join(lines), "srt")

    def _save_text_file(self, content, file_type):
        """保存文本文件的通用方法"""
        if not content.strip():
            self.status_text_edit.append("没有内容可保存")
            return

        # 设置默认文件名和过滤器
        if file_type == "srt":
            default_filename = f"{self.current_audio_filename}.srt"
            file_filter = "SRT字幕文件 (*.srt);;所有文件 (*)"
        else:
            default_filename = f"{self.current_audio_filename}.txt"
            file_filter = "文本文件 (*.txt);;所有文件 (*)"

        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            f"保存{file_type.upper()}文件",
            default_filename,
            file_filter
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_text_edit.append(f"文件已保存: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存文件失败: {str(e)}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec())
