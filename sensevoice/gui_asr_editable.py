import sys
import os
import configparser
import re
import platform
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QLineEdit, QRadioButton, QButtonGroup, QCheckBox, QSpinBox,
    QGroupBox, QComboBox, QTextEdit, QFileDialog, QFormLayout, QTableWidget, 
    QTableWidgetItem, QHeaderView, QMenu
)
from PySide6.QtCore import QThread, Signal, Qt
import time
import funasr

class AsrGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SenseVoice 语音识别客户端")
        self.setGeometry(100, 100, 900, 600)

        # 初始化主窗口组件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局（左右分栏）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)  # 设置外边距
        self.main_layout.setSpacing(15)  # 左右面板之间的间距

        # 创建左右面板
        self.left_panel = QWidget()
        self.right_panel = QWidget()
        
        # 设置左右面板的布局
        self.left_layout = QVBoxLayout(self.left_panel)
        self.left_layout.setContentsMargins(5, 10, 5, 10)  # 减少左右边距
        self.right_layout = QVBoxLayout(self.right_panel)
        
        # 将左右面板添加到主布局
        self.main_layout.addWidget(self.left_panel, 40)  # 左侧面板占据40%宽度
        self.main_layout.addWidget(self.right_panel, 60)  # 右侧面板占据60%宽度

        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.model_cache = {}

        # --- 左侧控制面板组件构建 ---
        # 标题和描述
        self.title_label = QLabel("SenseVoice 语音识别 \n(带时间戳和情感识别)")
        self.title_label.setStyleSheet("font-size: 16pt; font-weight: bold;")
        
        self.description_label = QLabel("上传音频文件，选择运行设备和模型进行识别。支持情感标签识别、事件检测和多种输出格式。")
        self.description_label.setWordWrap(True)
        
        # 将标题和描述添加到左侧面板
        self.left_layout.addWidget(self.title_label)
        self.left_layout.addWidget(self.description_label)
        self.left_layout.addSpacing(15)

        # 音频文件选择
        self.audio_path_edit = QLineEdit()
        self.audio_path_edit.setPlaceholderText("请选择音频文件路径")
        self.audio_path_edit.setReadOnly(True)
        
        self.browse_button = QPushButton("浏览文件")
        self.browse_button.clicked.connect(self.browse_audio_file)
        
        # 创建文件选择布局
        audio_input_layout = QHBoxLayout()
        audio_input_layout.addWidget(self.audio_path_edit)
        audio_input_layout.addWidget(self.browse_button)
        self.left_layout.addLayout(audio_input_layout)
        self.left_layout.addSpacing(15)

        # 基础配置区域（仅设备选择）
        basic_config_layout = QVBoxLayout()
        
        # 设备选择
        device_layout = QHBoxLayout()
        self.device_label = QLabel("运行设备:")
        self.device_combo = QComboBox()
        self.device_combo.addItem("CPU", "cpu")
        self.device_combo.addItem("CUDA", "cuda")
        
        # 根据操作系统设置默认设备
        is_mac = platform.system().lower() == 'darwin'
        default_device_index = 0 if is_mac else 1  # Mac默认CPU(0)，其他系统默认CUDA(1)
        self.device_combo.setCurrentIndex(default_device_index)
        
        device_layout.addWidget(self.device_label)
        device_layout.addWidget(self.device_combo)
        device_layout.addStretch()
        
        basic_config_layout.addLayout(device_layout)
        self.left_layout.addLayout(basic_config_layout)
        self.left_layout.addSpacing(10)

        # 模型选择
        self.model_config_group = QGroupBox("模型配置")
        model_config_layout = QFormLayout(self.model_config_group)
        model_config_layout.setVerticalSpacing(15)  # 增加垂直间距
        model_config_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.AllNonFixedFieldsGrow)  # 允许字段增长
        
        # 设置模型选择下拉框
        self.asr_model_combo = QComboBox()
        self.asr_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)  # 根据内容调整大小
        self.asr_model_combo.setMinimumContentsLength(20)  # 设置最小显示长度
        
        self.vad_model_combo = QComboBox()
        self.vad_model_combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.vad_model_combo.setMinimumContentsLength(20)
        
        model_config_layout.addRow("ASR 模型:", self.asr_model_combo)
        model_config_layout.addRow("VAD 模型:", self.vad_model_combo)
        
        self.model_config_group.setMinimumHeight(150)  # 设置最小高度确保显示所有选项
        self.left_layout.addWidget(self.model_config_group)
        self.left_layout.addSpacing(20)

        # 识别按钮
        self.submit_button = QPushButton("开始识别")
        self.submit_button.setFixedHeight(40)
        self.submit_button.setStyleSheet("font-size: 12pt;")
        self.submit_button.clicked.connect(self.start_recognition)
        self.left_layout.addWidget(self.submit_button)
        self.left_layout.addSpacing(15)

        # 程序状态区域（移到左侧下方）
        status_header_layout = QHBoxLayout()
        self.status_label = QLabel("程序状态")
        self.status_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        self.clear_status_button = QPushButton("清空状态")
        self.clear_status_button.setMaximumWidth(80)
        self.clear_status_button.clicked.connect(lambda: self.status_text_edit.clear())
        
        status_header_layout.addWidget(self.status_label)
        status_header_layout.addStretch()
        status_header_layout.addWidget(self.clear_status_button)
        
        self.status_text_edit = QTextEdit()
        self.status_text_edit.setReadOnly(True)
        self.status_text_edit.setPlaceholderText("程序状态信息将显示在这里...")
        self.status_text_edit.setMaximumHeight(200)  # 限制状态区域高度
        
        # 将状态区域添加到左侧面板
        self.left_layout.addLayout(status_header_layout)
        self.left_layout.addWidget(self.status_text_edit)
        self.left_layout.addStretch(1)  # 在底部添加可伸展空间

        # --- 右侧显示面板 ---
        # 识别结果区域
        result_header_layout = QHBoxLayout()
        self.result_label = QLabel("识别结果表格")
        self.result_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        
        # 下载按钮
        self.download_txt_button = QPushButton("下载TXT")
        self.download_txt_button.setMaximumWidth(80)
        self.download_txt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_txt_button.clicked.connect(self.download_txt_result)
        self.download_txt_button.setToolTip("下载为纯文本格式")
        
        self.download_srt_button = QPushButton("下载SRT")
        self.download_srt_button.setMaximumWidth(80)
        self.download_srt_button.setEnabled(False)  # 初始禁用，有结果后启用
        self.download_srt_button.clicked.connect(self.download_srt_result)
        self.download_srt_button.setToolTip("下载为SRT字幕格式")
        
        result_header_layout.addWidget(self.result_label)
        result_header_layout.addStretch()
        result_header_layout.addWidget(self.download_txt_button)
        result_header_layout.addWidget(self.download_srt_button)
        
        # 添加时间戳和情感事件标签表格
        self.result_table = QTableWidget()
        self.result_table.setEditTriggers(QTableWidget.EditTrigger.DoubleClicked | QTableWidget.EditTrigger.EditKeyPressed)
        self.result_table.itemChanged.connect(self._on_table_item_changed)
        self.result_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_table.customContextMenuRequested.connect(self._show_table_context_menu)
        
        # 用于记录编辑状态和当前值
        self.combo_current_values = {}  # 记录下拉框当前值
        
        # 表格操作按钮
        table_actions_layout = QHBoxLayout()
        
        # 撤销按钮
        self.undo_button = QPushButton("撤销")
        self.undo_button.setEnabled(False)
        self.undo_button.clicked.connect(self._undo_edit)
        self.undo_button.setToolTip("撤销上一次编辑操作")
        
        # 恢复按钮
        self.redo_button = QPushButton("恢复")
        self.redo_button.setEnabled(False)
        self.redo_button.clicked.connect(self._redo_edit)
        self.redo_button.setToolTip("恢复撤销的编辑操作")
        
        # 重置编辑按钮
        self.reset_edits_button = QPushButton("重置到原始")
        self.reset_edits_button.setEnabled(False)
        self.reset_edits_button.clicked.connect(self._reset_table_edits)
        self.reset_edits_button.setToolTip("清除所有编辑，恢复到原始识别结果")
        
        table_actions_layout.addStretch()
        table_actions_layout.addWidget(self.undo_button)
        table_actions_layout.addWidget(self.redo_button)
        table_actions_layout.addWidget(self.reset_edits_button)
        
        # 添加到右侧面板（只保留识别结果表格）
        self.right_layout.addLayout(result_header_layout)
        self.right_layout.addWidget(self.result_table)
        self.right_layout.addLayout(table_actions_layout)
        
        # 设置左侧面板的尺寸限制
        from PySide6.QtWidgets import QSizePolicy
        self.left_panel.setMinimumWidth(400)  # 增加控制面板最小宽度以容纳状态信息
        self.left_panel.setMaximumWidth(550)  # 增加控制面板最大宽度
        
        # 右侧面板应可伸展
        self.right_panel.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )
        
        # 存储当前识别结果的信息，用于下载
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        
        # 编辑历史管理，用于撤销/恢复功能
        self.edited_cells = {}  # 记录被编辑过的单元格 {(row, col): True}
        self.original_table_data = []  # 保存原始表格数据用于重置
        self.original_combo_values = {}  # 保存原始下拉框值
        self.original_text_values = {}  # 保存原始文本值
        
        # 定义情感和事件标签
        self.emotion_labels = ['(无)', '<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>']
        self.event_labels = ['(无)', '<|BGM|>', '<|Speech|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>']
        
        # 加载模型配置
        self.load_model_config()

    def browse_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择音频文件", "", "音频文件 (*.wav *.mp3 *.flac *.m4a *.pcm)")
        if file_path:
            self.audio_path_edit.setText(file_path)
            self.status_text_edit.append(f"已选择音频文件: {file_path}")

    def load_model_config(self):
        # 统一的配置文件路径
        config_paths = [
            # os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_mac.conf"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf"),
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    self.status_text_edit.append(f"读取配置文件 {path} 失败: {e}")
                    continue

        if not loaded_path:
            self.status_text_edit.append("错误: 未找到配置文件。请确保 model.conf 或 model_mac.conf 位于 sensevoice 目录下。")
            self.asr_model_combo.addItem("错误: 未找到配置文件")
            self.vad_model_combo.addItem("错误: 未找到配置文件")
            return

        self.status_text_edit.append(f"成功从 {loaded_path} 加载模型配置。")

        def populate_combo(combo, section_name, model_dict):
            combo.clear()
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    combo.addItem(name)
                    model_dict[name] = path_or_id
                if not model_dict:
                     combo.addItem(f"无模型 (在 {section_name})")
            else:
                self.status_text_edit.append(f"警告: 配置文件中未找到 [{section_name}] 部分。")
                combo.addItem(f"无模型 (无 {section_name})")

        populate_combo(self.asr_model_combo, "asr_models_dir", self.asr_models_config)
        populate_combo(self.vad_model_combo, "vad_models_dir", self.vad_models_config)
        
        # 为VAD模型添加"不使用"选项
        if self.vad_model_combo.count() > 0 and "无模型" not in self.vad_model_combo.itemText(0) and "错误:" not in self.vad_model_combo.itemText(0):
            self.vad_model_combo.insertItem(0, "None (不使用)")
            self.vad_models_config["None (不使用)"] = None 
            # 默认选择第1个实际模型，而不是"None (不使用)"
            if self.vad_model_combo.count() > 1:
                self.vad_model_combo.setCurrentIndex(1)

    def start_recognition(self):
        audio_file = self.audio_path_edit.text()
        if not audio_file or not os.path.exists(audio_file):
            self.status_text_edit.append("错误: 请先选择一个有效的音频文件。")
            return

        device = self.device_combo.currentData()
        
        asr_model_name = self.asr_model_combo.currentText()
        vad_model_name = self.vad_model_combo.currentText()

        asr_model_path = self.asr_models_config.get(asr_model_name)
        vad_model_path = self.vad_models_config.get(vad_model_name)

        if not asr_model_path or "错误:" in asr_model_name or "无模型" in asr_model_name:
            self.status_text_edit.append("错误: 请选择一个有效的 ASR 模型。")
            return
        
        if "错误:" in vad_model_name or "无模型" in vad_model_name or vad_model_name == "None (不使用)": 
            vad_model_path = None

        # 清空结果区域，准备显示新的识别结果
        self.result_table.clearContents()
        self.result_table.setRowCount(0)
        self.result_label.setText("识别结果表格")
        self.download_txt_button.setEnabled(False)
        self.download_srt_button.setEnabled(False)
        self.reset_edits_button.setEnabled(False)
        
        # 清空编辑历史
        self._init_edit_history()
        
        # 保存当前识别参数用于下载
        self.current_audio_filename = os.path.splitext(os.path.basename(audio_file))[0]
        
        # 状态信息输出到状态区域
        self.status_text_edit.append(f"开始识别: {os.path.basename(audio_file)}")
        self.status_text_edit.append(f"  设备: {self.device_combo.currentText()}")
        self.status_text_edit.append(f"  ASR 模型: {asr_model_name}")
        self.status_text_edit.append(f"  VAD 模型: {vad_model_name if vad_model_path else '不使用'}")
        self.status_text_edit.append("")

        self.submit_button.setEnabled(False)
        self.submit_button.setText("正在识别中...")

        self.recognition_thread = RecognitionWorker(
            audio_file, device, 
            asr_model_path, vad_model_path,
            self.model_cache
        )
        self.recognition_thread.progress.connect(self.update_progress)
        self.recognition_thread.finished.connect(self.recognition_finished)
        self.recognition_thread.error.connect(self.recognition_error)
        self.recognition_thread.start()

    def update_progress(self, message):
        self.status_text_edit.append(message)

    def recognition_finished(self, result_text, processing_time, table_data):
        # 状态信息更新
        self.status_text_edit.append(f"识别完成，耗时: {processing_time:.2f} 秒")
        
        # 更新标题
        self.result_label.setText(f"识别结果表格 (耗时: {processing_time:.2f} 秒)")
        
        # 显示表格数据
        if table_data:
            self._populate_results_table(table_data)
            self.reset_edits_button.setEnabled(True)
            # 保存原始数据用于重置
            self.original_table_data = table_data.copy()
            # 初始化编辑历史
            self._init_edit_history()
            
            # 启用下载按钮
            self.download_txt_button.setEnabled(True)
            self.download_srt_button.setEnabled(True)
        else:
            # 如果没有表格数据，显示空表格
            self.result_table.setRowCount(0)
            self.status_text_edit.append("警告: 没有识别到有效的时间戳数据")
            self.reset_edits_button.setEnabled(False)
            
            # 禁用下载按钮
            self.download_txt_button.setEnabled(False)
            self.download_srt_button.setEnabled(False)
        
        # 保存识别结果
        self.current_recognition_result = result_text
        
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def recognition_error(self, error_message):
        self.status_text_edit.append(f"--- 错误 ---")
        self.status_text_edit.append(error_message)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("开始识别")

    def _on_table_item_changed(self, item):
        """处理表格项编辑事件"""
        if not item:
            return

        row = item.row()
        col = item.column()
        new_text = item.text().strip()

        # 只处理识别文本列(第5列)的编辑
        if col == 5:  # 识别文本列
            text_key = (row, col)
            original_text = self.original_text_values.get(text_key, "")

            # 如果新值与原始值不同，标记为已编辑
            if new_text != original_text:
                self.edited_cells[text_key] = True
            else:
                # 如果恢复到原始值，移除编辑标记
                if text_key in self.edited_cells:
                    del self.edited_cells[text_key]

            # 更新按钮状态
            self._update_edit_buttons_state()

            # 更新下载数据
            self._update_result_text_from_table()
    

    
    def _undo_edit(self):
        """撤销编辑：将最近编辑的单元格恢复到原始值"""
        if not self.edited_cells:
            return

        # 获取最后一个被编辑的单元格（按编辑顺序）
        last_edited_key = list(self.edited_cells.keys())[-1]
        row, col = last_edited_key

        # 根据列类型恢复原始值
        if col in [3, 4]:  # 下拉框列（情感标签、事件标签）
            combo = self.result_table.cellWidget(row, col)
            if combo:
                original_value = self.original_combo_values.get(last_edited_key, "(无)")

                # 暂时断开信号
                combo.currentTextChanged.disconnect()
                combo.setCurrentText(original_value)
                # 更新当前值记录
                self.combo_current_values[last_edited_key] = original_value
                # 重新连接信号
                combo.currentTextChanged.connect(self._create_combo_handler(row, col))

                col_name = "情感标签" if col == 3 else "事件标签"
                self.status_text_edit.append(f"已撤销第{row+1}行{col_name}到原始值: '{original_value}'")

        elif col == 5:  # 文本列
            original_text = self.original_text_values.get(last_edited_key, "")

            # 暂时断开信号
            self.result_table.itemChanged.disconnect(self._on_table_item_changed)
            item = self.result_table.item(row, col)
            if item:
                item.setText(original_text)
            # 重新连接信号
            self.result_table.itemChanged.connect(self._on_table_item_changed)

            self.status_text_edit.append(f"已撤销第{row+1}行识别文本到原始值: '{original_text}'")

        # 移除编辑标记
        del self.edited_cells[last_edited_key]

        # 更新按钮状态
        self._update_edit_buttons_state()

        # 更新下载数据
        self._update_result_text_from_table()
    
    def _redo_edit(self):
        """恢复功能已简化：不再支持恢复操作"""
        # 新的撤销逻辑只支持撤销到原始值，不支持恢复
        pass
    
    def _reset_table_edits(self):
        """重置表格编辑，恢复到原始识别结果"""
        if hasattr(self, 'original_table_data') and self.original_table_data:
            # 重新填充表格数据（会重新初始化所有下拉框）
            self._populate_results_table(self.original_table_data)
            # 清空编辑历史
            self._init_edit_history()
            self._update_edit_buttons_state()
            self.status_text_edit.append("已重置表格内容到原始识别结果")
            self._update_result_text_from_table()
        else:
            self.status_text_edit.append("没有原始数据可重置")
    
    def _update_result_text_from_table(self):
        """根据表格数据更新内部数据结构（用于下载功能）"""
        if not self.result_table.isVisible() or self.result_table.rowCount() == 0:
            return
        
        # 重新生成结果文本用于下载
        lines = []
        for row in range(self.result_table.rowCount()):
            id_item = self.result_table.item(row, 0)
            start_time_item = self.result_table.item(row, 1)
            end_time_item = self.result_table.item(row, 2)
            emotion_combo = self.result_table.cellWidget(row, 3)
            event_combo = self.result_table.cellWidget(row, 4)
            text_item = self.result_table.item(row, 5)
            
            if all(item is not None for item in [id_item, start_time_item, end_time_item, text_item]):
                subtitle_id = id_item.text()
                start_time = start_time_item.text()
                end_time = end_time_item.text()
                emotion = emotion_combo.currentText().strip() if emotion_combo else ""
                event = event_combo.currentText().strip() if event_combo else ""
                text = text_item.text().strip()

                # 处理"(无)"标签，转换为空字符串
                if emotion == "(无)":
                    emotion = ""
                if event == "(无)":
                    event = ""

                # 生成带标签的文本
                full_text = ""
                if emotion:
                    full_text += emotion
                if event:
                    full_text += event
                full_text += text
                
                # 始终生成SRT格式用于内部存储
                lines.append(subtitle_id)
                lines.append(f"{start_time} --> {end_time}")
                lines.append(full_text)
                lines.append("")
        
        # 更新当前识别结果用于下载
        if lines:
            self.current_recognition_result = '\n'.join(lines)

    def _init_edit_history(self):
        """初始化编辑历史"""
        self.edited_cells = {}
        self.combo_current_values = {}
        # 注意：不清空原始值，因为它们需要保持原始数据
        self._update_edit_buttons_state()
    
    def _update_edit_buttons_state(self):
        """更新撤销/恢复按钮的状态"""
        self.undo_button.setEnabled(len(self.edited_cells) > 0)
        self.redo_button.setEnabled(False)  # 不再支持恢复功能
    
    def _show_table_context_menu(self, position):
        """显示表格右键菜单"""
        if self.result_table.rowCount() == 0:
            return
        
        menu = QMenu(self)
        
        # 获取当前选中的项
        current_item = self.result_table.itemAt(position)
        if current_item:
            # 只在识别文本列显示编辑菜单
            if current_item.column() == 5:  # 识别文本
                edit_action = menu.addAction("编辑此项")
                edit_action.triggered.connect(lambda: self.result_table.editItem(current_item))
        
        # 通用菜单项
        menu.addSeparator()
        copy_row_action = menu.addAction("复制此行")
        copy_row_action.triggered.connect(lambda: self._copy_table_row(current_item.row() if current_item else 0))
        
        menu.exec(self.result_table.mapToGlobal(position))
    
    def _copy_table_row(self, row):
        """复制表格行到剪贴板"""
        if row >= self.result_table.rowCount():
            return
        
        row_data = []
        # 使用实际的列数而不是硬编码
        for col in range(self.result_table.columnCount()):
            item = self.result_table.item(row, col)
            row_data.append(item.text() if item else "")
        
        # 复制到剪贴板
        from PySide6.QtGui import QClipboard
        clipboard = QApplication.clipboard()
        clipboard.setText('\t'.join(row_data))
        self.status_text_edit.append(f"已复制第 {row + 1} 行到剪贴板")
    
    def download_txt_result(self):
        """下载TXT格式的识别结果（纯文本格式）"""
        if self.result_table.rowCount() == 0:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return

        # 从表格生成纯文本格式
        lines = []
        for row in range(self.result_table.rowCount()):
            emotion_combo = self.result_table.cellWidget(row, 3)
            event_combo = self.result_table.cellWidget(row, 4)
            text_item = self.result_table.item(row, 5)
            
            if text_item:
                emotion = emotion_combo.currentText().strip() if emotion_combo else ""
                event = event_combo.currentText().strip() if event_combo else ""
                text = text_item.text().strip()

                # 处理"(无)"标签，转换为空字符串
                if emotion == "(无)":
                    emotion = ""
                if event == "(无)":
                    event = ""

                # 组合完整文本
                full_text = ""
                if emotion:
                    full_text += emotion
                if event:
                    full_text += event
                full_text += text
                
                if full_text:
                    lines.append(full_text)
        
        content = '\n'.join(lines) if lines else "没有识别结果"

        # 设置默认文件名
        default_filename = f"{self.current_audio_filename}.txt" if self.current_audio_filename else "recognition_result.txt"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存TXT格式结果", 
            default_filename, 
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_text_edit.append(f"TXT格式结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存TXT文件失败: {str(e)}")
    
    def download_srt_result(self):
        """下载SRT格式的识别结果（字幕格式）"""
        if self.result_table.rowCount() == 0:
            self.status_text_edit.append("错误: 没有可下载的识别结果。")
            return

        # 从表格生成SRT格式
        lines = []
        for row in range(self.result_table.rowCount()):
            id_item = self.result_table.item(row, 0)
            start_item = self.result_table.item(row, 1)
            end_item = self.result_table.item(row, 2)
            emotion_combo = self.result_table.cellWidget(row, 3)
            event_combo = self.result_table.cellWidget(row, 4)
            text_item = self.result_table.item(row, 5)
            
            if all(item is not None for item in [id_item, start_item, end_item, text_item]):
                subtitle_id = id_item.text()
                start_time = start_item.text()
                end_time = end_item.text()
                emotion = emotion_combo.currentText().strip() if emotion_combo else ""
                event = event_combo.currentText().strip() if event_combo else ""
                text = text_item.text().strip()

                # 处理"(无)"标签，转换为空字符串
                if emotion == "(无)":
                    emotion = ""
                if event == "(无)":
                    event = ""

                # 生成带标签的文本
                full_text = ""
                if emotion:
                    full_text += emotion
                if event:
                    full_text += event
                full_text += text
                
                lines.append(subtitle_id)
                lines.append(f"{start_time} --> {end_time}")
                lines.append(full_text)
                lines.append("")
        
        content = '\n'.join(lines) if lines else "没有识别结果"

        # 设置默认文件名
        default_filename = f"{self.current_audio_filename}.srt" if self.current_audio_filename else "recognition_result.srt"
        
        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存SRT格式结果", 
            default_filename, 
            "SRT字幕文件 (*.srt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.status_text_edit.append(f"SRT格式结果已保存到: {file_path}")
            except Exception as e:
                self.status_text_edit.append(f"保存SRT文件失败: {str(e)}")

    def _populate_results_table(self, table_data):
        """填充识别结果表格"""
        self.result_table.clearContents()
        if not table_data:
            self.result_table.setRowCount(0)
            return

        # 隐藏垂直表头（行号），避免与ID列重复
        self.result_table.verticalHeader().setVisible(False)

        # 设置表格列标题 - SenseVoice特有的列结构
        headers = ["编号", "开始时间", "结束时间", "情感标签", "事件标签", "识别文本"]
        self.result_table.setColumnCount(len(headers))
        self.result_table.setHorizontalHeaderLabels(headers)
        
        # 填充数据
        self.result_table.setRowCount(len(table_data))
        for row_idx, row_data in enumerate(table_data):
            for col_idx, cell_data in enumerate(row_data):
                if col_idx == 3:  # 情感标签列使用下拉框
                    combo = QComboBox()
                    combo.addItems(self.emotion_labels)
                    # 设置当前值
                    current_value = str(cell_data).strip()

                    # 保存真实的原始值（可能是空字符串或具体标签）
                    original_value = current_value

                    # 只有当原始数据为空字符串时，才在界面上显示为"(无)"
                    if current_value == "" or current_value == "None":
                        display_value = "(无)"
                    else:
                        display_value = current_value

                    if display_value in self.emotion_labels:
                        combo.setCurrentText(display_value)
                    else:
                        combo.setCurrentIndex(0)  # 默认为"(无)"
                        display_value = "(无)"

                    # 记录当前显示值到全局字典
                    combo_key = (row_idx, col_idx)
                    self.combo_current_values[combo_key] = display_value
                    # 保存原始值（用于撤销）
                    self.original_combo_values[combo_key] = display_value

                    # 连接信号，在值变化前先记录旧值
                    combo.currentTextChanged.connect(self._create_combo_handler(row_idx, col_idx))
                    self.result_table.setCellWidget(row_idx, col_idx, combo)
                elif col_idx == 4:  # 事件标签列使用下拉框
                    combo = QComboBox()
                    combo.addItems(self.event_labels)
                    # 设置当前值
                    current_value = str(cell_data).strip()

                    # 保存真实的原始值（可能是空字符串或具体标签）
                    original_value = current_value

                    # 只有当原始数据为空字符串时，才在界面上显示为"(无)"
                    if current_value == "" or current_value == "None":
                        display_value = "(无)"
                    else:
                        display_value = current_value

                    if display_value in self.event_labels:
                        combo.setCurrentText(display_value)
                    else:
                        combo.setCurrentIndex(0)  # 默认为"(无)"
                        display_value = "(无)"

                    # 记录当前显示值到全局字典
                    combo_key = (row_idx, col_idx)
                    self.combo_current_values[combo_key] = display_value
                    # 保存原始值（用于撤销）
                    self.original_combo_values[combo_key] = display_value

                    # 连接信号，在值变化前先记录旧值
                    combo.currentTextChanged.connect(self._create_combo_handler(row_idx, col_idx))
                    self.result_table.setCellWidget(row_idx, col_idx, combo)
                else:
                    item = QTableWidgetItem(str(cell_data))
                    if col_idx == 5:  # 识别文本列可编辑
                        item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                        item.setToolTip("双击编辑识别文本")
                        # 保存原始文本值
                        text_key = (row_idx, col_idx)
                        self.original_text_values[text_key] = str(cell_data)
                    else:  # 编号、开始时间、结束时间列设为只读
                        item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                        item.setToolTip("此列不可编辑")
                    self.result_table.setItem(row_idx, col_idx, item)
        
        # 设置列宽自适应
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        # 让识别文本列可以拉伸
        self.result_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)
    
    def _create_combo_handler(self, row, col):
        """创建下拉框变化处理器"""
        def handler(text):
            self._on_combo_changed(row, col, text)
        return handler
    
    def _on_combo_changed(self, row, col, new_text):
        """处理下拉框选择改变事件"""
        combo_key = (row, col)

        # 获取原始值
        original_value = self.original_combo_values.get(combo_key, "(无)")

        # 更新当前值记录
        self.combo_current_values[combo_key] = new_text

        # 如果新值与原始值不同，标记为已编辑
        if new_text != original_value:
            self.edited_cells[combo_key] = True
        else:
            # 如果恢复到原始值，移除编辑标记
            if combo_key in self.edited_cells:
                del self.edited_cells[combo_key]

        # 更新按钮状态
        self._update_edit_buttons_state()

        # 更新下载数据
        self._update_result_text_from_table()

class RecognitionWorker(QThread):
    progress = Signal(str)
    finished = Signal(str, float, list)  # Added float for processing time and list for table data
    error = Signal(str)

    def __init__(self, audio_file, device, 
                 asr_model_path, vad_model_path, model_cache_dict):
        super().__init__()
        self.audio_file = audio_file
        self.device = device
        self.asr_model_path = asr_model_path
        self.vad_model_path = vad_model_path
        self.model_cache = model_cache_dict

    def _ms2srt(self, ms):
        """时间戳格式化函数"""
        h = ms // 3600000
        m = (ms % 3600000) // 60000
        s = (ms % 60000) // 1000
        ms_r = ms % 1000
        return f"{h:02d}:{m:02d}:{s:02d},{ms_r:03d}"

    def run(self):
        try:
            self.progress.emit("正在准备模型...")
            model_key = f"{self.device}_{self.asr_model_path}_{str(self.vad_model_path)}"

            if model_key in self.model_cache:
                model = self.model_cache[model_key]
                self.progress.emit(f"从缓存加载模型")
            else:
                self.progress.emit(f"首次加载模型 (可能需要一些时间)")
                model_kwargs = {
                    "disable_update": True,
                    "device": self.device,
                    "model": self.asr_model_path
                }
                
                if self.vad_model_path:
                    model_kwargs["vad_model"] = self.vad_model_path
                    model_kwargs["vad_kwargs"] = {"max_single_segment_time": 30000}
                
                model = funasr.AutoModel(**model_kwargs)
                self.model_cache[model_key] = model
                self.progress.emit("模型加载完成.")

            self.progress.emit(f"开始识别音频文件: {os.path.basename(self.audio_file)}")
            
            generate_kwargs = {
                "input": self.audio_file,
                "cache": {},
                "language": "auto",
                "batch_size_s": 60,
                "merge_vad": True,
                "merge_length_s": 15,
                "output_timestamp": True  # 始终输出时间戳
            }

            start_process_time = time.time()
            rec_result = model.generate(**generate_kwargs)
            end_process_time = time.time()
            processing_time = end_process_time - start_process_time
            self.progress.emit("原始识别结果获取完毕, 正在格式化...")
            
            output_lines = []
            table_data = []  # 用于表格显示的数据
            
            # 始终使用时间戳模式，构建 SRT 样式和表格数据
            if rec_result:
                raw = rec_result[0].get('text', '')
                timestamps = rec_result[0].get('timestamp', [])
                
                # 情感和事件标签
                emo_tags = {'<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>'}
                event_tags = {'<|Speech|>', '<|BGM|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>', '<|Event_UNK|>'}
                
                parts = raw.split('<|zh|>')
                offset = 0
                segments = []
                
                for part in parts:
                    if not part:
                        continue
                    m = re.match(r'((?:<\|[^|]+\|>)+)', part)
                    if m:
                        # 提取情感和事件标签
                        all_tags = re.findall(r'<\|[^|]+\|>', m.group(1))
                        emotion_tags = [t for t in all_tags if t in emo_tags]
                        event_tags_found = [t for t in all_tags if t in event_tags]
                        
                        # 合并标签
                        emotion_str = ''.join(emotion_tags) if emotion_tags else ''
                        event_str = ''.join(event_tags_found) if event_tags_found else ''
                        
                        content = part[len(m.group(1)):].strip()
                    else:
                        emotion_str = ''
                        event_str = ''
                        content = part.strip()
                    
                    length = len(content)
                    if length == 0:
                        continue
                    
                    st = timestamps[offset][0] if offset < len(timestamps) else 0
                    et = timestamps[offset + length - 1][1] if offset + length - 1 < len(timestamps) else st
                    segments.append((emotion_str, event_str, content, st, et))
                    offset += length
                
                # 格式化输出和生成表格数据
                for idx, (emotion, event, txt, st, et) in enumerate(segments, 1):
                    # SRT格式输出
                    output_lines.append(str(idx))
                    output_lines.append(f"{self._ms2srt(st)} --> {self._ms2srt(et)}")
                    
                    # 合并标签和文本
                    full_text = emotion + event + txt
                    output_lines.append(full_text)
                    output_lines.append("")
                    
                    # 表格数据：[编号, 开始时间, 结束时间, 情感标签, 事件标签, 识别文本]
                    table_data.append([
                        idx,
                        self._ms2srt(st),
                        self._ms2srt(et),
                        emotion,
                        event,
                        txt
                    ])
            
            if not output_lines:
                output_lines = ["识别结果为空。"]
            
            self.finished.emit("\n".join(output_lines), processing_time, table_data)

        except Exception as e:
            self.error.emit(f"识别过程中发生错误: {str(e)}\n请检查模型路径、音频文件格式和依赖项是否正确安装。\n错误详情: {type(e).__name__}: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = AsrGui()
    window.show()
    sys.exit(app.exec()) 